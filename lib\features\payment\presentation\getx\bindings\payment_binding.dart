import 'package:get/get.dart';
import 'package:point_of_sale/injection_controller.dart';

import '../../../../home/<USER>/getx/controllers/order_details_controller.dart';
import '../controllers/pay_order_controller.dart';
import '../controllers/payment_methods_controller.dart';
import '../controllers/payment_controller.dart';

class PaymentBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<OrderDetailsController>(
      () => OrderDetailsController(
        InjectionController().getIt(),
      ),
    );

    Get.lazyPut<PaymentMethodsController>(
      () => PaymentMethodsController(
        InjectionController().getIt(),
      ),
    );
    Get.lazyPut<PayOrderController>(
      () => PayOrderController(
        InjectionController().getIt(),
      ),
    );
    Get.lazyPut<PaymentController>(
      () => PaymentController(
          // InjectionController().getIt(),
          ),
    );
  }
}
