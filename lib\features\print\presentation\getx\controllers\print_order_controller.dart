import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:flutter_esc_pos_network/flutter_esc_pos_network.dart';
import 'package:flutter_esc_pos_utils/flutter_esc_pos_utils.dart';
import 'package:get/get.dart';
import 'package:image/image.dart' as img;
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:point_of_sale/core/services/cash_data_source.dart';
import 'package:point_of_sale/core/widgets/failed_snack_bar.dart';
import 'package:point_of_sale/core/widgets/success_snack_bar.dart';
import 'package:point_of_sale/features/home/<USER>/getx/controllers/order_details_controller.dart';
import 'package:point_of_sale/features/print/presentation/getx/controllers/print_controller.dart';
import 'package:point_of_sale/features/print/presentation/views/print_view.dart';
import 'package:point_of_sale/features/settings/presentation/getx/controllers/printers_ip_address_controller.dart';
import 'package:printing/printing.dart';
import 'package:screenshot/screenshot.dart';

class PrintOrderController extends GetxController {
  final ScreenshotController screenshotController = ScreenshotController();

  /// Fetches the logo bytes (via cache) or throws if something goes wrong.
  Future<Uint8List> _fetchLogoBytes(String url) async {
    try {
      // 1. Use DefaultCacheManager to download (or retrieve from cache) the file.
      final File file = await DefaultCacheManager().getSingleFile(url);
      // 2. Read raw bytes
      final Uint8List bytes = await file.readAsBytes();
      return bytes;
    } catch (err) {
      throw Exception('Failed to load logo from network: $err');
    }
  }

  Future<void> printInvoice(PrintController controller) async {
    // Check if running on web
    if (kIsWeb) {
      await _printInvoiceWeb(controller);
      return;
    }

    // Mobile/Desktop printing logic
    await _printInvoiceMobile(controller);
  }

  /// Web-specific printing method - uses image capture of the invoice widget
  Future<void> _printInvoiceWeb(PrintController controller) async {
    try {
      Get.log('Printing invoice on web platform using image capture');

      // Capture the invoice widget as an image and convert to PDF
      await _printInvoiceAsImagePdf(controller);

      // Show success message
      successSnackBar(
        'Invoice ready for printing. Please use your browser\'s print dialog.',
      );
    } catch (e) {
      Get.log('Web print error: $e');
      failedSnaskBar(
        'Failed to print invoice: $e',
      );
    }
  }

  /// Print invoice by capturing the widget as an image and converting to PDF
  Future<void> _printInvoiceAsImagePdf(PrintController controller) async {
    try {
      Get.log('Capturing invoice widget as image');

      // Create the invoice widget
      final invoiceWidget = Invoice(controller: controller);

      // Capture the widget as an image
      final imageBytes = await _captureWidgetAsImage(invoiceWidget);

      // Convert image to PDF
      final pdfBytes = await _createPdfFromImage(imageBytes);

      // Open print dialog
      await Printing.layoutPdf(
        onLayout: (PdfPageFormat format) async => pdfBytes,
        name: 'Invoice_${controller.orderNumber}',
        format: PdfPageFormat.a4,
        usePrinterSettings: true,
      );

      Get.log('Image-based PDF print dialog opened successfully');
    } catch (e) {
      Get.log('Image-based PDF printing error: $e');
      rethrow;
    }
  }

  /// Capture a Flutter widget as an image using screenshot package
  Future<Uint8List> _captureWidgetAsImage(Widget widget) async {
    try {
      Get.log('Starting widget capture using screenshot package');

      // Create a properly structured widget for capture
      final captureWidget = MaterialApp(
        home: Scaffold(
          backgroundColor: Colors.white,
          body: SingleChildScrollView(
            child: Container(
              width: 400,
              padding: const EdgeInsets.all(16),
              color: Colors.white,
              child: widget,
            ),
          ),
        ),
        debugShowCheckedModeBanner: false,
      );

      // Use screenshot controller to capture the widget
      final imageBytes = await screenshotController.captureFromWidget(
        captureWidget,
        pixelRatio: 2.0,
        context: Get.context,
      );

      Get.log(
          'Widget capture completed successfully - ${imageBytes.length} bytes');
      return imageBytes;
    } catch (e) {
      Get.log('Error capturing widget as image: $e');
      rethrow;
    }
  }

  /// Create a PDF from an image
  Future<Uint8List> _createPdfFromImage(Uint8List imageBytes) async {
    final pdf = pw.Document();

    final image = pw.MemoryImage(imageBytes);

    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(10),
        build: (pw.Context context) {
          return pw.Center(
            child: pw.Image(
              image,
              fit: pw.BoxFit.contain,
            ),
          );
        },
      ),
    );

    return pdf.save();
  }

  /// Print ESC/POS data to a single printer
  Future<void> _printToSinglePrinter(String printerIP, List<int> bytes) async {
    try {
      Get.log('Printing to $printerIP');

      const port = 9100;
      final printer = PrinterNetworkManager(printerIP, port: port);

      final result = await printer.printTicket(bytes);
      if (result == PosPrintResult.success) {
        Get.log('Successfully printed to $printerIP');
      } else {
        throw Exception('Failed to print to printer $printerIP: $result');
      }
    } catch (e) {
      Get.log('Error printing to $printerIP: $e');
      rethrow;
    }
  }

  /// Mobile/Desktop printing method - now uses image capture
  Future<void> _printInvoiceMobile(PrintController controller) async {
    // Get printer IPs
    final printersController = Get.find<PrintersIPAddressController>();
    final printerIPs = printersController.getAllPrinterIPs();

    if (printerIPs.isEmpty) {
      Get.snackbar(
        'Error'.tr,
        'No printers configured. Please add printer IPs in settings.'.tr,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Get.theme.colorScheme.error,
        colorText: Get.theme.colorScheme.onError,
      );
      return;
    }

    Get.log(
        'Starting ESC/POS print job to ${printerIPs.length} printer(s): $printerIPs');

    try {
      // Get cached invoice data
      final cashDataSource = Get.find<CashDataSource>();
      final invoiceData = cashDataSource.getInvoiceData();
      final orderDetailsController = Get.find<OrderDetailsController>();

      // Calculate totals
      final invoiceNumber = controller.orderNumber.toString();
      final double subtotal = controller.totalPrice;
      final vat = invoiceData['vat'] as String;
      final double vatAmount = subtotal * (double.parse(vat) / 100);
      final double totalIncludingVat = subtotal + vatAmount;

      // Generate ESC/POS commands using existing method
      final profile = await CapabilityProfile.load();
      final escPosBytes = await _generateEscPosCommands(
        controller,
        orderDetailsController,
        invoiceData,
        invoiceNumber,
        subtotal,
        vatAmount,
        totalIncludingVat,
        profile,
      );

      // Print to all configured printers
      final List<Future<void>> printTasks = [];

      for (String printerIP in printerIPs) {
        printTasks.add(_printToSinglePrinter(printerIP, escPosBytes));
      }

      // Wait for all print tasks to complete
      try {
        await Future.wait(printTasks);
        Get.snackbar(
          'Success'.tr,
          'Invoice printed to ${printerIPs.length} printer(s) successfully',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Get.theme.colorScheme.primary,
          colorText: Get.theme.colorScheme.onPrimary,
        );
      } catch (e) {
        // Some printers failed, but show partial success if any succeeded
        Get.snackbar(
          'Warning'.tr,
          'Some printers failed. Check printer connections.',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Get.theme.colorScheme.error,
          colorText: Get.theme.colorScheme.onError,
        );
      }
    } catch (e) {
      Get.log('Mobile print error: $e');
      Get.snackbar(
        'Error',
        'Failed to print invoice: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Get.theme.colorScheme.error,
        colorText: Get.theme.colorScheme.onError,
      );
    }
  }

  /// Generate ESC/POS commands for the invoice matching the image design
  Future<List<int>> _generateEscPosCommands(
    PrintController controller,
    OrderDetailsController orderDetailsController,
    Map<String, dynamic> invoiceData,
    String invoiceNumber,
    double subtotal,
    double vatAmount,
    double totalIncludingVat,
    CapabilityProfile profile,
  ) async {
    Get.log('Starting ESC/POS generation...');
    Get.log('Invoice data: $invoiceData');

    // Create generator and bytes list outside try block for error handling
    final generator = Generator(PaperSize.mm80, profile);
    final List<int> bytes = <int>[];

    try {
      final restaurantName =
          invoiceData['restaurantName'] as String? ?? 'Restaurant';
      final vatNumber = invoiceData['vatNumber'] as String? ?? '';
      final commerce = invoiceData['commerce'] as String? ?? '';
      final mainAddress = invoiceData['mainAddress'] as String? ?? '';
      final cashierName = invoiceData['cashierName'] as String? ?? '';
      final vat = invoiceData['vat'] as String? ?? '15';
      final websiteUrl = invoiceData['websiteUrl'] as String? ?? '';
      final logoUrl = invoiceData['logo'] as String? ?? '';

      bytes.addAll(List<int>.from(generator.reset()));

      // Logo section
      if (logoUrl.isNotEmpty) {
        try {
          final logoBytes = await _fetchLogoBytes(logoUrl);
          final logoImage = img.decodeImage(logoBytes);
          if (logoImage != null) {
            // Resize the image to fit the receipt width (80mm thermal printer)
            // Typical 80mm printer has ~576 pixels width at 203 DPI
            final int targetWidth =
                500; // Adjust based on your printer's capabilities
            final int targetHeight =
                (logoImage.height * targetWidth / logoImage.width).round();
            final resizedImage = img.copyResize(
              logoImage,
              width: targetWidth,
              height: targetHeight,
              interpolation: img.Interpolation.average,
            );

            bytes.addAll(List<int>.from(generator.image(resizedImage)));
          }
        } catch (e) {
          Get.log('Error printing logo: $e');
        }
      }

      bytes.addAll(List<int>.from(generator.emptyLines(3)));
      bytes.addAll(List<int>.from(generator.cut()));

      Get.log('Generated ${bytes.length} bytes for thermal printer');
      return bytes;
    } catch (e) {
      Get.log('Error in ESC/POS generation: $e');
      // Minimal fallback
      bytes.addAll(List<int>.from(generator.text('INVOICE ERROR')));
      bytes.addAll(List<int>.from(generator.cut()));
      return bytes;
    }
  }
}
