import 'dart:io';
import 'dart:typed_data';

import 'package:flutter/services.dart'
    show rootBundle; // لاستيراد الخطّ من assets
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:point_of_sale/features/home/<USER>/getx/controllers/order_details_controller.dart';
import 'package:point_of_sale/features/print/presentation/getx/controllers/print_controller.dart';
import 'package:printing/printing.dart';
import 'package:qr_flutter/qr_flutter.dart';

class PrintOrderController extends GetxController {
  /// 1) دالة لتحميل الخطّ العربي من ملف في assets
  Future<pw.Font> _loadArabicFont() async {
    final fontData = await rootBundle.load("assets/fonts/Cairo-SemiBold.ttf");
    return pw.Font.ttf(fontData);
  }

  /// 2) Fetches the logo bytes (via cache) or throws if something goes wrong.
  Future<Uint8List> _fetchLogoBytes(String url) async {
    try {
      // 1. Use DefaultCacheManager to download (or retrieve from cache) the file.
      final File file = await DefaultCacheManager().getSingleFile(url);
      // 2. Read raw bytes
      final Uint8List bytes = await file.readAsBytes();
      return bytes;
    } catch (err) {
      throw Exception('Failed to load logo from network: $err');
    }
  }

  Future<void> printInvoice(PrintController controller) async {
    // بيانات الشركة والمستخدم
    final logoUrl =
        controller.loginController.loginTaskModel.data?.company?[0].logo ?? '';

    final commerce = controller.loginController.loginTaskModel.data?.company?[0]
            .commercialRegisteration ??
        '';

    final cashierName =
        controller.loginController.loginTaskModel.data?.user?.name ?? 'Cashier';

    final restaurntName = controller.loginController.loginTaskModel.data
            ?.company?[0].name?.first.text ??
        'Restaurant';

    final invoiceNumber = controller.orderNumber.toString();

    final vatNumber =
        controller.loginController.loginTaskModel.data?.company?[0].vatNumber ??
            '0';
    final vat = controller.loginController.loginTaskModel.data?.company?[0]
            .settings?.invoice?[14].value ??
        '0';

    final mainAddress = controller.loginController.loginTaskModel.data
            ?.company?[0].mainAddress?.first.text ??
        'No Address';
    final websiteUrl = controller
            .loginController.loginTaskModel.data?.company?[0].websiteUrl ??
        'Ahmed M. Okal';
    final double subtotal = controller.total.value; // المجموع بدون الضريبة
    final double vatAmount = subtotal * double.parse(vat) / 100; // قيمة الضريبة
    final double totalIncludingVat =
        subtotal + vatAmount; // الإجمالي شامل الضريبة

    try {
      // 1) Resolve order details controller
      final orderDetailsController = Get.find<OrderDetailsController>();

      // 2) Create a new PDF document
      final pdf = pw.Document();

      // 3) حمّل الخطّ العربي (Cairo) لمرة واحدة
      final arabicFont = await _loadArabicFont();

      // 4) Pre‐fetch الصورة الشبكية (logo) إذا كان الرابط غير فارغ
      Uint8List? logoBytes;
      if (logoUrl.isNotEmpty) {
        try {
          logoBytes = await _fetchLogoBytes(logoUrl);
        } catch (_) {
          logoBytes = null; // إذا فشل التحميل، سنتخطّى الشعار
        }
      }

      // 5) Generate QR code data (binary) لاستخدامه كـ MemoryImage
      final qrData = await QrPainter(
        data: controller
                .loginController.loginTaskModel.data?.company?[0].websiteUrl ??
            '',
        version: QrVersions.auto,
        errorCorrectionLevel: QrErrorCorrectLevel.M,
      ).toImageData(500);

      // 6) أضف صفحة واحدة بصيغة رول-80
      pdf.addPage(
        pw.Page(
          pageFormat: PdfPageFormat.roll80,
          // margin: pw.EdgeInsets.all(10),
          build: (pw.Context context) {
            return pw.Directionality(
              textDirection: pw.TextDirection.rtl,
              child: pw.ListView(
                children: [
                  // ——— الشعار (logo) ———
                  if (logoBytes != null)
                    pw.Center(
                      child: pw.Image(
                        pw.MemoryImage(logoBytes),
                        width: 150,
                        height: 100,
                        fit: pw.BoxFit.cover,
                      ),
                    ),

                  if (logoBytes != null) pw.SizedBox(height: 10),
                  pw.SizedBox(height: 50),

                  // ——— اسم المطعم والعنوان الرئيسي للفاتورة ———
                  pw.Text(
                    restaurntName,
                    textAlign: pw.TextAlign.center,
                    style: pw.TextStyle(
                      font: arabicFont, // استخدام الخط العربي
                      fontSize: 8,
                      fontWeight: pw.FontWeight.bold,
                    ),
                  ),
                  pw.Text(
                    'simpifiedTaxInvoice'.tr, // المفترض أنها ترجمة جاهزة للعربي
                    textAlign: pw.TextAlign.center,
                    style: pw.TextStyle(
                      font: arabicFont,
                      fontSize: 8,
                      fontWeight: pw.FontWeight.bold,
                    ),
                  ),

                  // رقم الفاتورة في مستطيل
                  pw.Container(
                    height: 30,
                    width: 140,
                    decoration: pw.BoxDecoration(
                      border: pw.Border.all(width: 1, color: PdfColors.black),
                      borderRadius:
                          const pw.BorderRadius.all(pw.Radius.circular(6)),
                    ),
                    child: pw.Center(
                      child: pw.Text(
                        'Order #${invoiceNumber.trim()}',
                        style: pw.TextStyle(
                          font: arabicFont,
                          fontSize: 16,
                          fontWeight: pw.FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                  pw.SizedBox(height: 8),

                  // ——— بيانات VAT و C.R و POS و الكاشير و التاريخ ———
                  pw.Column(
                    children: [
                      pw.Row(
                        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                        children: [
                          pw.Text(
                            'VAT',
                            style: pw.TextStyle(
                              font: arabicFont,
                              fontSize: 8,
                              fontWeight: pw.FontWeight.bold,
                            ),
                          ),
                          pw.Text(
                            vatNumber,
                            style: pw.TextStyle(
                              font: arabicFont,
                              fontSize: 8,
                              fontWeight: pw.FontWeight.bold,
                            ),
                          ),
                          pw.Text(
                            'الرقم الضريبي',
                            style: pw.TextStyle(
                              font: arabicFont,
                              fontSize: 8,
                            ),
                          ),
                        ],
                      ),
                      pw.SizedBox(height: 4),
                      pw.Row(
                        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                        children: [
                          pw.Text(
                            'C.R',
                            style: pw.TextStyle(
                              font: arabicFont,
                              fontSize: 8,
                              fontWeight: pw.FontWeight.bold,
                            ),
                          ),
                          pw.Text(
                            commerce,
                            style: pw.TextStyle(
                              font: arabicFont,
                              fontSize: 8,
                              fontWeight: pw.FontWeight.bold,
                            ),
                          ),
                          pw.Text(
                            'السجل التجاري',
                            style: pw.TextStyle(
                              font: arabicFont,
                              fontSize: 8,
                            ),
                          ),
                        ],
                      ),
                      pw.SizedBox(height: 4),
                      pw.Row(
                        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                        children: [
                          pw.Text(
                            'POS',
                            style: pw.TextStyle(
                              font: arabicFont,
                              fontSize: 8,
                              fontWeight: pw.FontWeight.bold,
                            ),
                          ),
                          pw.Text(
                            mainAddress,
                            style: pw.TextStyle(
                              font: arabicFont,
                              fontSize: 6,
                              fontWeight: pw.FontWeight.bold,
                            ),
                          ),
                          pw.Text(
                            'نقطة البيع',
                            style: pw.TextStyle(
                              font: arabicFont,
                              fontSize: 8,
                            ),
                          ),
                        ],
                      ),
                      pw.SizedBox(height: 4),
                      pw.Row(
                        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                        children: [
                          pw.Text(
                            'Cashier',
                            style: pw.TextStyle(
                              font: arabicFont,
                              fontSize: 8,
                              fontWeight: pw.FontWeight.bold,
                            ),
                          ),
                          pw.Text(
                            cashierName,
                            style: pw.TextStyle(
                              font: arabicFont,
                              fontSize: 8,
                              fontWeight: pw.FontWeight.bold,
                            ),
                          ),
                          pw.Text(
                            'الكاشير',
                            style: pw.TextStyle(
                              font: arabicFont,
                              fontSize: 8,
                            ),
                          ),
                        ],
                      ),
                      pw.SizedBox(height: 4),
                      pw.Row(
                        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                        children: [
                          pw.Text(
                            'Date',
                            style: pw.TextStyle(
                              font: arabicFont,
                              fontSize: 8,
                              fontWeight: pw.FontWeight.bold,
                            ),
                          ),
                          pw.Text(
                            DateFormat('yyyy/MM/dd hh:mm a')
                                .format(DateTime.now()),
                            style: pw.TextStyle(
                              font: arabicFont,
                              fontSize: 8,
                              fontWeight: pw.FontWeight.bold,
                            ),
                          ),
                          pw.Text(
                            'التاريخ',
                            style: pw.TextStyle(
                              font: arabicFont,
                              fontSize: 8,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),

                  pw.Divider(),

                  // ——— رؤوس عمود البنود ———
                  pw.Row(
                    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                    children: [
                      pw.Column(
                        children: [
                          pw.Text(
                            'Qty',
                            style: pw.TextStyle(
                              font: arabicFont,
                              fontSize: 10,
                              fontWeight: pw.FontWeight.bold,
                            ),
                          ),
                          pw.SizedBox(height: 2),
                          pw.Text(
                            'الكمية',
                            style: pw.TextStyle(
                              font: arabicFont,
                              fontSize: 8,
                              fontWeight: pw.FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      pw.Column(
                        children: [
                          pw.Text(
                            'Item',
                            style: pw.TextStyle(
                              font: arabicFont,
                              fontSize: 10,
                              fontWeight: pw.FontWeight.bold,
                            ),
                          ),
                          pw.SizedBox(height: 2),
                          pw.Text(
                            'الصنف',
                            style: pw.TextStyle(
                              font: arabicFont,
                              fontSize: 8,
                              fontWeight: pw.FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      pw.Column(
                        children: [
                          pw.Text(
                            'Price',
                            style: pw.TextStyle(
                              font: arabicFont,
                              fontSize: 10,
                              fontWeight: pw.FontWeight.bold,
                            ),
                          ),
                          pw.SizedBox(height: 2),
                          pw.Text(
                            'السعر',
                            style: pw.TextStyle(
                              font: arabicFont,
                              fontSize: 8,
                              fontWeight: pw.FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                  pw.Divider(),

                  // ——— كل سطر من البنود ———
                  ...?orderDetailsController.orderDetailsModel.data?.items?.map(
                    (item) => pw.Row(
                      mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                      children: [
                        pw.Text(
                          item.qty ?? '0',
                          style: pw.TextStyle(
                            font: arabicFont,
                            fontSize: 10,
                          ),
                        ),
                        pw.Text(
                          item.productName ?? '',
                          style: pw.TextStyle(
                            font: arabicFont,
                            fontSize: 10,
                          ),
                        ),
                        pw.Text(
                          '${item.price ?? '0.00'} ${'SR'.tr}',
                          style: pw.TextStyle(
                            font: arabicFont,
                            fontSize: 10,
                          ),
                        ),
                      ],
                    ),
                  ),

                  pw.Divider(thickness: 1),

                  // ——— صف الإجمالي ———
                  pw.Row(
                    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                    children: [
                      pw.Text(
                        'المجموع بدون الضريبة - Subtotal',
                        style: pw.TextStyle(
                          font: arabicFont,
                          fontSize: 6,
                          fontWeight: pw.FontWeight.bold,
                        ),
                      ),
                      pw.Text(
                        '${controller.total.value.toStringAsFixed(2)} SR',
                        style: pw.TextStyle(
                          font: arabicFont,
                          fontSize: 8,
                          fontWeight: pw.FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  pw.Row(
                    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                    children: [
                      pw.Text(
                        '%($vat) الضريبة - VAT',
                        style: pw.TextStyle(
                          font: arabicFont,
                          fontSize: 6,
                          fontWeight: pw.FontWeight.bold,
                        ),
                      ),
                      pw.Text(
                        '${controller.total.value.toStringAsFixed(2)} SR',
                        style: pw.TextStyle(
                          font: arabicFont,
                          fontSize: 8,
                          fontWeight: pw.FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  pw.Row(
                    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                    children: [
                      pw.Text(
                        'الإجمالي شامل الضريبة - Total including VAT',
                        style: pw.TextStyle(
                          font: arabicFont,
                          fontSize: 6,
                          fontWeight: pw.FontWeight.bold,
                        ),
                      ),
                      pw.Text(
                        '$totalIncludingVat SR',
                        style: pw.TextStyle(
                          font: arabicFont,
                          fontSize: 10,
                          fontWeight: pw.FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  pw.Divider(),
                  pw.SizedBox(height: 30),

                  // ——— كود الاستجابة السريعة (QR) ———
                  pw.Center(
                    child: pw.Image(
                        pw.MemoryImage(
                          qrData?.buffer.asUint8List() ?? Uint8List(0),
                        ),
                        width: 150,
                        height: 150,
                        fit: pw.BoxFit.cover),
                  ),

                  pw.SizedBox(height: 5),

                  // ——— نص في الفوتر ———
                  pw.Center(
                    child: pw.Text(
                      'Powerd by $websiteUrl',
                      style: pw.TextStyle(
                        font: arabicFont,
                        fontSize: 10,
                      ),
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      );

      // 7) أرسل مستند الـ PDF للطابعة أو المعاينة
      await Printing.layoutPdf(
        onLayout: (PdfPageFormat format) async => pdf.save(),
      );
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to print invoice: $e',
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }
}
