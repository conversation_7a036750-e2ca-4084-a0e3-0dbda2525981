import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:point_of_sale/core/services/theme_service.dart';
import 'package:point_of_sale/core/utils/app_colors.dart';
import 'package:point_of_sale/core/utils/app_text_style.dart';
import 'package:point_of_sale/core/utils/size_config.dart';
import 'package:point_of_sale/core/widgets/language_drop_down_button.dart';

class SettingsPage extends StatelessWidget {
  final ThemeService _themeService = Get.find<ThemeService>();

  SettingsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        color: AppColors.background,
        padding: EdgeInsets.all(AppSize.width(20)),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Theme settings
            Text(
              'appearance'.tr,
              style: AppTextStyle.primary18800,
            ),
            SizedBox(height: AppSize.height(15)),
            Card(
              child: ListTile(
                title: Text(
                  'dark_mode'.tr,
                  style: AppTextStyle.primary16500,
                ),
                trailing: Obx(() => Switch(
                      value: _themeService.isDarkMode,
                      onChanged: (value) {
                        _themeService.switchTheme();
                      },
                      activeColor: AppColors.primaryColor,
                    )),
              ),
            ),

            SizedBox(height: AppSize.height(10)),

            // Theme Demo Button

            // Language settings
            SizedBox(height: AppSize.height(30)),
            Text(
              'language'.tr,
              style: AppTextStyle.primary18800,
            ),
            SizedBox(height: AppSize.height(15)),
            LanguageDropDownButton(),
          ],
        ),
      ),
    );
  }
}
